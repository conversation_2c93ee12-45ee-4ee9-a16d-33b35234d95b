<script lang="ts" setup>
  import { computed, reactive, ref } from 'vue';

  import { message } from 'ant-design-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddAuxiliaryPop',
  });

  const props = withDefaults(defineProps<Props>(), {
    auxiliaryType: 'customer',
  });

  const emits = defineEmits(['refresh']);

  interface Props {
    auxiliaryType?:
      | 'customer'
      | 'department'
      | 'employee'
      | 'inventory'
      | 'project'
      | 'supplier';
  }

  const visible = ref(false);
  const formRef = ref<any>(null);
  const currentSubject = ref<any>(null); // 存储当前选中的科目信息

  // 使用科目数据hooks
  const { accountSubjects, extractLeafSubjects } = useAccountSubjects();

  const formState = reactive({
    code: '999999', // 辅助核算编码，默认999999
    name: '', // 辅助核算名称
    type: props.auxiliaryType, // 辅助核算类型
  });

  // 辅助核算类型选项
  const auxiliaryTypeOptions = [
    { label: '客户', value: 'customer' },
    { label: '供应商', value: 'supplier' },
    { label: '员工', value: 'employee' },
    { label: '部门', value: 'department' },
    { label: '项目', value: 'project' },
    { label: '存货', value: 'inventory' },
  ];

  // 辅助核算类型映射（从API类型到组件类型）
  const auxiliaryTypeMap: Record<string, string> = {
    c: 'customer', // 客户
    d: 'department', // 部门
    e: 'employee', // 员工
    i: 'inventory', // 存货
    p: 'project', // 项目
    s: 'supplier', // 供应商
  };

  // 重置表单
  const resetForm = () => {
    formState.code = '999999';
    formState.name = '';
    formState.type = props.auxiliaryType;
  };

  // 根据科目code查找完整的科目信息
  const findSubjectByCode = (code: string) => {
    if (!accountSubjects.value || !code) return null;

    const leafSubjects = extractLeafSubjects(accountSubjects.value);
    return leafSubjects.find((subject) => subject.code === code);
  };

  // 计算当前辅助类别的显示标签
  const currentTypeLabel = computed(() => {
    const option = auxiliaryTypeOptions.find(
      (opt) => opt.value === formState.type,
    );
    return option ? option.label : formState.type;
  });

  // 判断是否应该禁用辅助类别选择器
  // 新增科目支持所有类型选择，现有科目根据配置限制类型
  const isTypeDisabled = computed(() => {
    if (!currentSubject.value) return false;

    // 检查是否为新增科目
    const isNewSubject =
      currentSubject.value.isNewSubject || currentSubject.value.p_account_code;

    if (isNewSubject) {
      // 新增科目支持所有类型选择，不禁用选择器
      return false;
    }

    // 现有科目：当有辅助核算类型时，固定死类型，无法选择
    return !!(
      currentSubject.value?.assistantType ||
      (currentSubject.value &&
        formState.type &&
        formState.type !== props.auxiliaryType)
    );
  });

  const open = (
    subjectInfo?: any,
    auxiliaryType?: string,
    llmAuxiliaryValue?: string,
  ) => {
    resetForm();
    currentSubject.value = subjectInfo;
    console.log('subjectInfo44444', subjectInfo);
    // 验证科目是否支持辅助核算
    if (subjectInfo) {
      const subjectName =
        subjectInfo.fullName ||
        subjectInfo.text ||
        subjectInfo.label ||
        subjectInfo.name ||
        '当前科目';

      // 检查是否为新增科目（通过 isNewSubject 标记或 p_account_code 字段判断）
      const isNewSubject =
        subjectInfo.isNewSubject || subjectInfo.p_account_code;

      if (isNewSubject) {
        // 新增科目默认开启辅助核算，支持所有类型选择
        console.log(
          '✅ 检测到新增科目，默认开启辅助核算，支持所有类型选择:',
          subjectName,
        );
      } else {
        // 现有科目需要验证辅助核算配置

        // 如果传入的科目信息不完整，尝试从全局科目数据中获取完整信息
        // 如果传入的科目信息不完整，尝试从全局科目数据中获取完整信息
        let completeSubjectInfo = subjectInfo;

        // 获取科目代码，优先从code字段，如果为空则从text字段解析
        let subjectCode = subjectInfo.code;
        if (!subjectCode && subjectInfo.text) {
          // 从text字段解析科目代码，格式如："1122 应收账款 00209 中国人民解放军61419部队"
          const textParts = subjectInfo.text.split(' ').filter(Boolean);
          if (textParts.length > 0) {
            subjectCode = textParts[0];
          }
        }

        console.log('subjectInfo.code', subjectInfo.code);
        console.log('解析出的科目代码:', subjectCode);

        if (!subjectInfo.useAssistant && subjectCode) {
          const foundSubject = findSubjectByCode(subjectCode);
          console.log('foundSubject222', foundSubject);
          if (foundSubject) {
            completeSubjectInfo = foundSubject;
            console.log('✅ 从全局科目数据中找到完整的科目信息:', foundSubject);
          }
        }

        // 检查科目是否开启了辅助核算
        console.log('subjectInfo222', subjectInfo);
        console.log('completeSubjectInfo', completeSubjectInfo);
        if (!completeSubjectInfo.useAssistant) {
          message.warning(
            `科目"${subjectName}"未开启辅助核算功能，无法新增辅助项目。请在会计科目设置中开启该科目的辅助核算功能。`,
          );
          return;
        }

        // 检查是否有辅助核算类型
        if (!completeSubjectInfo.assistantType && !auxiliaryType) {
          message.warning(
            `科目"${subjectName}"未配置辅助核算类型，无法新增辅助项目。请在会计科目设置中配置该科目的辅助核算类型。`,
          );
          return;
        }

        // 如果传入了辅助核算类型，验证是否与科目的辅助核算类型匹配
        if (auxiliaryType && completeSubjectInfo.assistantType) {
          const mappedSubjectType =
            auxiliaryTypeMap[completeSubjectInfo.assistantType] ||
            completeSubjectInfo.assistantType;
          const mappedInputType =
            auxiliaryTypeMap[auxiliaryType] || auxiliaryType;

          if (mappedSubjectType !== mappedInputType) {
            const assistantTypeLabels: Record<string, string> = {
              customer: '客户',
              department: '部门',
              employee: '员工',
              inventory: '存货',
              project: '项目',
              supplier: '供应商',
            };

            const subjectTypeName =
              assistantTypeLabels[mappedSubjectType] || mappedSubjectType;
            const inputTypeName =
              assistantTypeLabels[mappedInputType] || mappedInputType;

            message.warning(
              `科目"${subjectName}"的辅助核算类型为"${subjectTypeName}"，与要新增的"${inputTypeName}"类型不匹配。请选择正确的辅助核算类型。`,
            );
            return;
          }
        }

        // 更新当前科目信息为完整信息
        currentSubject.value = completeSubjectInfo;
      }
    } else {
      // 如果没有科目信息，提示用户先选择科目
      message.warning('请先选择科目，然后再新增辅助项目。');
      return;
    }

    // 如果传入了辅助核算类型，设置表单的类型
    if (auxiliaryType) {
      // 映射API类型到组件类型
      const mappedType = auxiliaryTypeMap[auxiliaryType] || auxiliaryType;
      if (auxiliaryTypeOptions.some((option) => option.value === mappedType)) {
        formState.type = mappedType as any;
      }
    } else if (currentSubject.value?.assistantType) {
      // 如果没有传入辅助核算类型，但科目信息中有，则使用科目的辅助核算类型
      const mappedType =
        auxiliaryTypeMap[currentSubject.value.assistantType] ||
        currentSubject.value.assistantType;
      if (auxiliaryTypeOptions.some((option) => option.value === mappedType)) {
        formState.type = mappedType as any;
      }
    }

    // 如果有LLM输出的辅助项目值，预填充到名称字段
    if (llmAuxiliaryValue) {
      formState.name = llmAuxiliaryValue;
      console.log('预填充LLM输出的辅助项目值:', llmAuxiliaryValue);
    }

    console.log('🔧 AddAuxiliaryPop open:', {
      assistantType: currentSubject.value?.assistantType,
      auxiliaryType,
      currentSubject: currentSubject.value,
      finalType: formState.type,
      originalSubjectInfo: subjectInfo,
      useAssistant: currentSubject.value?.useAssistant,
    });

    visible.value = true;
  };

  const handleOk = async () => {
    try {
      await formRef.value.validate();

      // 构建新辅助核算对象（新增辅助项目不设置ID，由后端生成）
      const newAuxiliary = {
        accountSetId: 0,
        accountTitleId: null,
        code: formState.code,
        customerId: 0,
        freezeStatus: 'normal',
        fullName: formState.name,
        fullNameExtend: null,
        fullNameWithSpace: null,
        id: null, // 新增辅助项目不设置ID，由后端生成
        // 标记这是新增的辅助项目
        isNewAuxiliary: true,
        name: formState.name,
        pinYinInitial: null,
        remark: null,
        repeatCode: 0,
        taxpayerNum: null,
        titles: [],
        type: formState.type,
      };

      message.success('新增辅助核算成功');
      // 通知凭证页面刷新辅助核算数据，并传递科目信息
      emitter.emit('account_voucher_auxiliary_added', {
        auxiliary: newAuxiliary,
        subject: currentSubject.value,
      });
      // 通知父组件刷新
      emits('refresh');
      visible.value = false;
    } catch (error) {
      console.error('新增辅助核算失败:', error);
      message.error('新增辅助核算失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };

  defineExpose({
    open,
  });
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="新增辅助项目"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <!-- 显示当前科目信息 -->
      <a-alert
        v-if="currentSubject"
        :message="`当前科目: ${currentSubject.fullName || currentSubject.text || currentSubject.label || '未知科目'}`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 显示辅助类别提示 -->
      <a-alert
        v-if="isTypeDisabled"
        message="辅助类别已根据当前科目的辅助核算类型自动确定，无法修改"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-alert
        v-else-if="
          currentSubject &&
          (currentSubject.isNewSubject || currentSubject.p_account_code)
        "
        message="新增科目默认开启辅助核算，可以选择任意辅助类别"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <a-form
        :model="formState"
        autocomplete="off"
        ref="formRef"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="编码" name="code">
          <a-input
            v-model:value="formState.code"
            placeholder="默认999999"
            disabled
          />
        </a-form-item>
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="formState.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item
          label="辅助类别"
          name="type"
          :rules="[{ required: true, message: '请选择辅助类别' }]"
        >
          <!-- 当有科目信息且科目有辅助核算类型时，固定死类型，无法选择 -->
          <a-input
            v-if="isTypeDisabled"
            :value="currentTypeLabel"
            disabled
            placeholder="根据科目类型自动确定"
          />
          <a-select
            v-else
            v-model:value="formState.type"
            placeholder="请选择辅助类别"
            :options="auxiliaryTypeOptions"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
